const express = require("express");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");

const router = express.Router();

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let usersTableCreated = false;

// إنشاء جدول المستخدمين إذا لم يكن موجودًا
const createUsersTable = async () => {
  if (usersTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً
  try {
    // التحقق من وجود جدول المستخدمين
    const [tables] = await pool.promise().query(
      "SHOW TABLES LIKE 'users'"
    );
    
    if (tables.length === 0) {
      // إنشاء جدول المستخدمين
      await pool.promise().query(`
        CREATE TABLE users (
          id int NOT NULL AUTO_INCREMENT,
          username varchar(50) NOT NULL,
          password varchar(255) NOT NULL,
          permissions JSON DEFAULT NULL,
          created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY username (username)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
      `);
      
      // إنشاء مستخدم admin افتراضي
      const hashedPassword = await bcrypt.hash('123456', 10);
      await pool.promise().query(
        "INSERT INTO users (username, password, permissions) VALUES (?, ?, ?)",
        [
          'admin', 
          hashedPassword, 
          JSON.stringify({
            can_view: true,
            can_add: true,
            can_edit: true,
            can_delete: true,
            view_employees: true,
            view_vacations: true,
            view_contributions: true,
            view_rewards_deductions: true,
            view_rewards_list: true,
            add_reward: true,
            export_rewards: true,
            view_deductions_list: true,
            add_deduction: true,
            export_deductions: true,
            view_rewards_deductions_reports: true,
            view_custody: true,
            view_evaluation: true,
            view_training: true,
            add_training: true,
            edit_training: true,
            delete_training: true,
            view_training_reports: true,
            view_resignations: true,
            add_resignation: true,
            edit_resignation: true,
            delete_resignation: true,
            view_resignation_reports: true,
            // صلاحيات العهد المحددة
            edit_deliver_custody: true,
            delete_deliver_custody: true,
            edit_return_custody: true,
            delete_return_custody: true,
            view_import: true,
            manage_users: true
          })
        ]
      );
    } else {
      // التحقق من وجود عمود permissions
      const [columns] = await pool.promise().query(
        "SHOW COLUMNS FROM users LIKE 'permissions'"
      );
      
      if (columns.length === 0) {
        // إضافة عمود permissions
        await pool.promise().query(
          "ALTER TABLE users ADD COLUMN permissions JSON DEFAULT NULL AFTER password"
        );
        
        // تحديث المستخدم admin ليملك جميع الصلاحيات
        await pool.promise().query(
          "UPDATE users SET permissions = ? WHERE username = 'admin'",
          [JSON.stringify({
            can_view: true,
            can_add: true,
            can_edit: true,
            can_delete: true,
            view_employees: true,
            view_vacations: true,
            view_contributions: true,
            view_rewards_deductions: true,
            view_rewards_list: true,
            add_reward: true,
            export_rewards: true,
            view_deductions_list: true,
            add_deduction: true,
            export_deductions: true,
            view_rewards_deductions_reports: true,
            view_custody: true,
            view_evaluation: true,
            view_training: true,
            add_training: true,
            edit_training: true,
            delete_training: true,
            view_training_reports: true,
            view_resignations: true,
            add_resignation: true,
            edit_resignation: true,
            delete_resignation: true,
            view_resignation_reports: true,
            // صلاحيات العهد المحددة
            edit_deliver_custody: true,
            delete_deliver_custody: true,
            edit_return_custody: true,
            delete_return_custody: true,
            view_import: true,
            manage_users: true
          })]
        );
      }
    }
    
    usersTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول المستخدمين:', error);
    throw error;
  }
};

// تسجيل الدخول
router.post("/login", async (req, res) => {
  const { username, password } = req.body;

  try {
    // التأكد من وجود جدول المستخدمين
    await createUsersTable();
    
    // التحقق من وجود المستخدمين في الجدول
    const [checkUsers] = await pool.promise().query("SELECT COUNT(*) as count FROM users");
    console.log('عدد المستخدمين في الجدول:', checkUsers[0].count);
    
    // التحقق من وجود المستخدم admin
    const [checkAdmin] = await pool.promise().query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    console.log('هل يوجد مستخدم admin:', checkAdmin[0].count > 0);
    
    const [rows] = await pool.promise().query(
      "SELECT * FROM users WHERE username = ?",
      [username]
    );

    if (rows.length === 0) {
      console.log('اسم المستخدم غير موجود:', username);
      
      // إذا كان المستخدم هو admin وغير موجود، نقوم بإنشائه
      if (username === 'admin') {
        console.log('محاولة إنشاء مستخدم admin افتراضي');
        const hashedPassword = await bcrypt.hash('123456', 10);
        await pool.promise().query(
          "INSERT INTO users (username, password, permissions) VALUES (?, ?, ?)",
          [
            'admin', 
            hashedPassword, 
            JSON.stringify({
              can_view: true,
              can_add: true,
              can_edit: true,
              can_delete: true,
              view_employees: true,
              view_vacations: true,
              view_contributions: true,

              view_custody: true,
              view_evaluation: true,
              view_resignations: true,
              add_resignation: true,
              edit_resignation: true,
              delete_resignation: true,
              view_resignation_reports: true,
              view_import: true,
              manage_users: true
            })
          ]
        );
        console.log('تم إنشاء مستخدم admin بنجاح');
        
        // إعادة محاولة الحصول على المستخدم
        const [newRows] = await pool.promise().query(
          "SELECT * FROM users WHERE username = ?",
          [username]
        );
        
        if (newRows.length > 0) {
          console.log('تم العثور على المستخدم admin بعد إنشائه');
          rows.push(newRows[0]);
        } else {
          console.log('فشل في إنشاء المستخدم admin');
          return res.status(401).json({ error: "فشل في إنشاء المستخدم admin" });
        }
      } else {
        return res.status(401).json({ error: "اسم المستخدم غير موجود" });
      }
    }

    const user = rows[0];
    console.log('تم العثور على المستخدم:', user.username);

    // التحقق من كلمة المرور
    let isPasswordValid = false;
    
    // التحقق من كلمة المرور باستخدام bcrypt
    try {
      isPasswordValid = await bcrypt.compare(password, user.password);
    } catch (error) {
      console.error('خطأ في مقارنة كلمة المرور:', error);
      // في حالة حدوث خطأ في المقارنة، نتحقق إذا كان المستخدم هو admin وكلمة المرور هي 123456
      if (user.username === 'admin' && password === '123456') {
        isPasswordValid = true;
        
        // تحديث كلمة المرور المشفرة للمستخدم admin
        const hashedPassword = await bcrypt.hash('123456', 10);
        await pool.promise().query(
          "UPDATE users SET password = ? WHERE id = ?",
          [hashedPassword, user.id]
        );
      }
    }
    
    if (isPasswordValid) {
      // تحويل الصلاحيات إلى كائن JavaScript
      let permissions = {};
      if (user.permissions) {
        permissions = typeof user.permissions === 'string' 
          ? JSON.parse(user.permissions) 
          : user.permissions;
      } else if (user.username === 'admin') {
        // إذا لم تكن هناك صلاحيات محددة للمستخدم admin، نعطيه جميع الصلاحيات
        permissions = {
          can_view: true,
          can_add: true,
          can_edit: true,
          can_delete: true,
          view_employees: true,
          view_vacations: true,
          add_vacation: true,
          view_vacations_list: true,
          view_vacation_reports: true,
          view_contributions: true,
          view_rewards_deductions: true,
          view_rewards_list: true,
          add_reward: true,
          export_rewards: true,
          view_deductions_list: true,
          add_deduction: true,
          export_deductions: true,
          view_rewards_deductions_reports: true,
          view_custody: true,
          view_evaluation: true,
          view_training: true,
          add_training: true,
          edit_training: true,
          delete_training: true,
          view_training_reports: true,
          view_resignations: true,
          add_resignation: true,
          edit_resignation: true,
          delete_resignation: true,
          view_resignation_reports: true,
          view_extra_hours: true,
          add_extra_hour: true,
          edit_extra_hour: true,
          delete_extra_hour: true,
          view_extra_hour_reports: true,
          view_import: true,
          manage_users: true
        };
        
        // تحديث الصلاحيات في قاعدة البيانات
        await pool.promise().query(
          "UPDATE users SET permissions = ? WHERE id = ?",
          [JSON.stringify(permissions), user.id]
        );
      }

      const token = jwt.sign(
        { id: user.id, username: user.username },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '24h' }
      );

      console.log('تم تسجيل الدخول بنجاح');
      return res.json({
        token,
        user: {
          id: user.id,
          username: user.username,
          permissions: permissions
        }
      });
    }

    console.log('كلمة المرور غير صحيحة');
    return res.status(401).json({ error: "كلمة المرور غير صحيحة" });
  } catch (error) {
    console.error("خطأ في تسجيل الدخول:", error);
    res.status(500).json({ error: "حدث خطأ أثناء تسجيل الدخول" });
  }
});

// الحصول على جميع المستخدمين
router.get('/users', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    await createUsersTable();
    
    const [rows] = await pool.promise().query(
      "SELECT id, username, permissions, created_at FROM users ORDER BY id"
    );
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب المستخدمين:', error);
    res.status(500).json({ error: 'فشل في جلب المستخدمين' });
  }
});

// الحصول على مستخدم محدد
router.get('/users/:id', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const { id } = req.params;
    
    const [rows] = await pool.promise().query(
      "SELECT id, username, permissions, created_at FROM users WHERE id = ?",
      [id]
    );
    
    if (rows.length === 0) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    
    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب المستخدم:', error);
    res.status(500).json({ error: 'فشل في جلب المستخدم' });
  }
});

// إضافة مستخدم جديد
router.post('/users', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    await createUsersTable();
    
    const { username, password, permissions } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'يرجى توفير اسم المستخدم وكلمة المرور' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس الاسم
    const [existingUsers] = await pool.promise().query(
      "SELECT id FROM users WHERE username = ?",
      [username]
    );
    
    if (existingUsers.length > 0) {
      return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
    }
    
    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // إضافة المستخدم الجديد
    const [result] = await pool.promise().query(
      "INSERT INTO users (username, password, permissions) VALUES (?, ?, ?)",
      [username, hashedPassword, JSON.stringify(permissions)]
    );
    
    res.status(201).json({
      id: result.insertId,
      username,
      permissions
    });
  } catch (error) {
    console.error('خطأ في إضافة المستخدم:', error);
    res.status(500).json({ error: 'فشل في إضافة المستخدم' });
  }
});

// تحديث مستخدم
router.put('/users/:id', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const { id } = req.params;
    const { username, password, permissions } = req.body;
    
    // التحقق من وجود المستخدم
    const [existingUsers] = await pool.promise().query(
      "SELECT id, username FROM users WHERE id = ?",
      [id]
    );
    
    if (existingUsers.length === 0) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    
    // التحقق من عدم وجود مستخدم آخر بنفس الاسم
    if (username && username !== existingUsers[0].username) {
      const [duplicateUsers] = await pool.promise().query(
        "SELECT id FROM users WHERE username = ? AND id != ?",
        [username, id]
      );
      
      if (duplicateUsers.length > 0) {
        return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
      }
    }
    
    // تحضير البيانات للتحديث
    const updates = [];
    const values = [];
    
    if (username) {
      updates.push("username = ?");
      values.push(username);
    }
    
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      updates.push("password = ?");
      values.push(hashedPassword);
    }
    
    if (permissions) {
      updates.push("permissions = ?");
      values.push(JSON.stringify(permissions));
    }
    
    if (updates.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }
    
    // إضافة معرف المستخدم للقيم
    values.push(id);
    
    // تحديث المستخدم
    await pool.promise().query(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      values
    );
    
    res.json({ message: 'تم تحديث المستخدم بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error);
    res.status(500).json({ error: 'فشل في تحديث المستخدم' });
  }
});

// حذف مستخدم
router.delete('/users/:id', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const { id } = req.params;
    
    // التحقق من عدم حذف المستخدم admin
    const [adminUser] = await pool.promise().query(
      "SELECT username FROM users WHERE id = ?",
      [id]
    );
    
    if (adminUser.length > 0 && adminUser[0].username === 'admin') {
      return res.status(403).json({ error: 'لا يمكن حذف المستخدم admin' });
    }
    
    // حذف المستخدم
    const [result] = await pool.promise().query(
      "DELETE FROM users WHERE id = ?",
      [id]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error);
    res.status(500).json({ error: 'فشل في حذف المستخدم' });
  }
});

// الحصول على معلومات المستخدم الحالي
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const [rows] = await pool.promise().query(
      "SELECT id, username, permissions, created_at FROM users WHERE id = ?",
      [req.user.id]
    );
    
    if (rows.length === 0) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    
    const user = rows[0];
    let permissions = {};
    
    if (user.permissions) {
      permissions = typeof user.permissions === 'string' 
        ? JSON.parse(user.permissions) 
        : user.permissions;
    }
    
    res.json({
      id: user.id,
      username: user.username,
      permissions: permissions,
      created_at: user.created_at
    });
  } catch (error) {
    console.error('خطأ في جلب معلومات المستخدم:', error);
    res.status(500).json({ error: 'فشل في جلب معلومات المستخدم' });
  }
});

module.exports = router;