const express = require("express");
const mysql = require("mysql2");
const cors = require("cors");
const csv = require("csv-parse");
const fs = require("fs");
require("dotenv").config();

// استيراد مكتبة التواريخ
const DateUtils = require("./dateUtils");

// استيراد نظام تسجيل الأنشطة
const { logAction } = require("./activityLogger");

// استيراد middleware
const { authenticateToken } = require("./middleware/auth");

// دوال مساعدة لترجمة الأنواع
function getVacationTypeLabel(type) {
  const labels = {
    'casual': 'إجازة عارضة',
    'permission': 'إذن',
    'absence': 'غياب',
    'annual': 'إجازة سنوية',
    'unpaid': 'إجازة بدون راتب',
    'sick': 'إجازة مرضية',
    'official': 'إجازة رسمية'
  };
  return labels[type] || type;
}

// تحميل الـ routes
const authRoutes = require("./routes/auth");
const employeesRoutes = require("./routes/employees");
const filesRoutes = require("./routes/files");
const evaluationsRoutes = require("./routes/evaluations");
const contributionsRoutes = require("./routes/contributions");
const vacationsRoutes = require("./routes/vacations");
const rewardsDeductionsRoutes = require("./routes/rewards-deductions");
const resignationsRoutes = require("./routes/resignations");
const idealEmployeesRoutes = require("./routes/idealEmployees");
const utilitiesRoutes = require("./routes/utilities");
const debugRoutes = require("./routes/debug");
const maintenanceRoutes = require("./routes/maintenance");
const custodyRoutes = require("./routes/custody");
const trainingRoutes = require("./routes/training");
const salaryAdvanceRoutes = require("./routes/salaryAdvance");
const extraHoursRoutes = require("./routes/extraHours");
const activityLogRoutes = require("./routes/activityLog");

// إعدادات قاعدة البيانات الافتراضية
let dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "Hbkhbkhbk@123",
  database: process.env.DB_NAME || "hassan",
  port: parseInt(process.env.DB_PORT) || 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// إنشاء connection pool
let pool = mysql.createPool(dbConfig);

const app = express();
// السماح بتحديد المنفذ من خلال معلمات سطر الأوامر
const port = process.argv.includes('--port') ?
  parseInt(process.argv[process.argv.indexOf('--port') + 1]) : 5500;

// إتاحة pool للـ routes
app.locals.pool = pool;

// التحقق من الاتصال
pool.getConnection((err, connection) => {
  if (err) {
    console.error("فشل الاتصال بقاعدة البيانات:", err);
  } else {
    console.log("تم الاتصال بقاعدة البيانات بنجاح");
    connection.release();
  }
});

// إعدادات CORS
app.use(cors({
    origin: '*', // السماح لجميع المصادر
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true
}));

// إضافة middleware لتسجيل جميع الطلبات (معطل لتنظيف السجل)
// app.use((req, res, next) => {
//   console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
//   console.log('Headers:', req.headers);
//   if (req.body && Object.keys(req.body).length > 0) {
//     console.log('Body:', req.body);
//   }
//   next();
// });

app.use(express.json({ limit: '100mb' }));
app.use(express.urlencoded({ extended: true, limit: '100mb' }));
app.use(express.static(__dirname));

// استيراد إعدادات multer
const { upload } = require("./config/multer");

// استخدام الـ routes
app.use('/api', authRoutes);
app.use('/api/employees', employeesRoutes);
app.use('/api', filesRoutes);
app.use('/api/evaluations', evaluationsRoutes);
app.use('/api/contributions', contributionsRoutes);
app.use('/api/vacations', vacationsRoutes);
app.use('/api', rewardsDeductionsRoutes);
app.use('/api/resignations', resignationsRoutes);
app.use('/api/ideal-employees', idealEmployeesRoutes);
app.use('/api', utilitiesRoutes);
app.use('/api/debug', debugRoutes);
app.use('/api', maintenanceRoutes);
app.use('/api/custody', custodyRoutes);
app.use('/api/training', trainingRoutes);
app.use('/api/salary-advances', salaryAdvanceRoutes);
app.use('/api/extra-hours', extraHoursRoutes);
app.use('/api/activity-logs', activityLogRoutes);

// تم نقل دوال إنشاء الجداول إلى routes/utilities.js

// تم نقل دوال إنشاء الجداول إلى routes/utilities.js

// تم نقل جميع utility endpoints إلى routes/utilities.js


// ==================== EMPLOYEE FILES ENDPOINTS ====================
// تم نقل جميع endpoints الملفات إلى routes/files.js

// تسجيل الدخول - تم نقله إلى routes/auth.js

// تسجيل الخروج - تم نقله إلى routes/auth.js

// تم نقل دالة createUsersTable إلى routes/debug.js

// الحصول على جميع المستخدمين - تم نقله إلى routes/auth.js

// الحصول على مستخدم محدد - تم نقله إلى routes/auth.js

// إضافة مستخدم جديد - تم نقله إلى routes/auth.js

// تحديث مستخدم - تم نقله إلى routes/auth.js

// حذف مستخدم - تم نقله إلى routes/auth.js

// الحصول على معلومات المستخدم الحالي - تم نقله إلى routes/auth.js

// تم نقل endpoint status إلى routes/utilities.js

// تم نقل جميع maintenance endpoints إلى routes/maintenance.js

// استيراد بيانات CSV
app.post("/api/import", upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: "لم يتم تحميل أي ملف" });
  }

  const results = [];

  fs.createReadStream(req.file.path)
    .pipe(csv.parse({ 
      columns: true, 
      trim: true,
      skip_empty_lines: true,
      quote: '"',
      escape: '"',
      bom: true
    }))
    .on('data', (data) => {
      // تنظيف البيانات من علامات الاقتباس الزائدة
      const cleanData = {};
      Object.keys(data).forEach(key => {
        let value = data[key];
        if (typeof value === 'string') {
          value = value.replace(/^"|"$/g, '').trim();
        }
        cleanData[key] = value;
      });
      results.push(cleanData);
    })
    .on('end', () => {
      // حذف الملف المؤقت
      fs.unlinkSync(req.file.path);

      // إدخال البيانات في قاعدة البيانات
      const sql = `
            INSERT INTO employees (
                code, full_name, department, job_title, hire_date, address, qualification, 
                phone, birth_date, marital_status, children, national_id, social_insurance, 
                insurance_number, insurance_entity, insurance_start, insurance_job, 
                insurance_salary, worker_cost, company_cost, total_salary, health_card, 
                skill_level, skill_start, skill_end, skill_remaining, skill_job, 
                leave_balance, leave_used, leave_remaining, special_needs, photo, documents
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

      const insertPromises = results.map(row => {
        return new Promise((resolve, reject) => {
          const values = [
            row.code,
            row.full_name,
            row.department,
            row.job_title,
            row.hire_date,
            row.address,
            row.qualification,
            row.phone,
            row.birth_date,
            row.marital_status,
            row.children,
            row.national_id,
            row.social_insurance,
            row.insurance_number,
            row.insurance_entity,
            row.insurance_start,
            row.insurance_job,
            row.insurance_salary,
            row.worker_cost,
            row.company_cost,
            row.total_salary,
            row.health_card,
            row.skill_level,
            row.skill_start,
            row.skill_end,
            row.skill_remaining,
            row.skill_job,
            row.leave_balance,
            row.leave_used,
            row.leave_remaining,
            row.special_needs,
            row.photo,
            row.documents
          ];

          pool.query(sql, values, (err, result) => {
            if (err) {
              console.error("خطأ في إدخال البيانات:", err);
              reject(err);
            } else {
              resolve(result);
            }
          });
        });
      });

      Promise.all(insertPromises)
        .then(() => {
          res.json({
            success: true,
            message: `تم استيراد ${results.length} سجل بنجاح`,
            count: results.length
          });
        })
        .catch(error => {
          console.error("خطأ في استيراد البيانات:", error);
          res.status(500).json({
            success: false,
            error: "حدث خطأ أثناء استيراد البيانات"
          });
        });
    })
    .on('error', (error) => {
      console.error("خطأ في قراءة ملف CSV:", error);
      res.status(500).json({
        success: false,
        error: "حدث خطأ أثناء قراءة ملف CSV"
      });
    });
});

// إنشاء جدول المساهمات إذا لم يكن موجوداً
// تم نقل جميع maintenance endpoints إلى routes/maintenance.js

// تم نقل جميع endpoints المساهمات إلى routes/contributions.js

// دالة لتحويل التاريخ من صيغ مختلفة إلى YYYY-MM-DD باستخدام مكتبة التواريخ المحدثة
const formatDateForMySQL = (dateString) => {
  return DateUtils.formatDateForDatabase(dateString);
};

// استيراد بيانات من Excel
app.post('/api/import-excel', async (req, res) => {

    try {
        const employees = req.body;
        if (!Array.isArray(employees) || employees.length === 0) {
            return res.status(400).json({ error: 'لا توجد بيانات للاستيراد' });
        }

        const sql = `
            INSERT INTO employees (
                code, full_name, department, job_title, hire_date, address, qualification, 
                phone, birth_date, marital_status, children, national_id, social_insurance, 
                insurance_number, insurance_entity, insurance_start, insurance_job, 
                insurance_salary, worker_cost, company_cost, total_salary, health_card, 
                skill_level, skill_start, skill_end, skill_remaining, skill_job, 
                leave_balance, leave_used, leave_remaining, special_needs, photo, documents
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const insertPromises = employees.map((employee, index) => {
            return new Promise((resolve, reject) => {
                // دالة للتعامل مع القيم الفارغة والحقول الرقمية
                const safe = (key, isNumeric = false) => {
                    const value = employee[key];
                    if (value === undefined || value === "" || value === null) {
                        return isNumeric ? 0 : null;
                    }
                    return isNumeric ? (parseInt(value) || 0) : value;
                };

                // دالة للتعامل مع حقول التاريخ
                const safeDate = (key) => {
                    const value = employee[key];
                    if (value === undefined || value === "" || value === null) {
                        return null;
                    }
                    const formattedDate = formatDateForMySQL(value);
                    if (formattedDate === null && value.toString().trim() !== '') {

                    }
                    return formattedDate;
                };

                const values = [
                    safe("code"),
                    safe("full_name"),
                    safe("department"),
                    safe("job_title"),
                    safeDate("hire_date"), // معالجة التاريخ
                    safe("address"),
                    safe("qualification"),
                    safe("phone"),
                    safeDate("birth_date"), // معالجة التاريخ
                    safe("marital_status"),
                    safe("children"),
                    safe("national_id"),
                    safe("social_insurance"),
                    safe("insurance_number"),
                    safe("insurance_entity"),
                    safeDate("insurance_start"), // معالجة التاريخ
                    safe("insurance_job"),
                    safe("insurance_salary"),
                    safe("worker_cost"),
                    safe("company_cost"),
                    safe("total_salary"),
                    safe("health_card"),
                    safe("skill_level"),
                    safeDate("skill_start"), // معالجة التاريخ
                    safeDate("skill_end"), // معالجة التاريخ
                    safe("skill_remaining", true), // حقل رقمي
                    safe("skill_job"),
                    safe("leave_balance", true), // حقل رقمي
                    safe("leave_used", true), // حقل رقمي
                    safe("leave_remaining", true), // حقل رقمي
                    safe("special_needs"),
                    safe("photo"),
                    safe("documents")
                ];

                pool.query(sql, values, (err, result) => {
                    if (err) {

                        reject({
                            row: index + 1,
                            error: err.message,
                            data: employee
                        });
                    } else {

                        resolve(result);
                    }
                });
            });
        });

        try {
            await Promise.all(insertPromises);
            res.json({
                message: `تم استيراد ${employees.length} موظف بنجاح`,
                total_rows: employees.length,
                successful_imports: employees.length,
                failed_imports: 0
            });
        } catch (errors) {
            // في حالة فشل بعض الصفوف
            const failedRows = Array.isArray(errors) ? errors : [errors];
            const successfulRows = employees.length - failedRows.length;

            res.status(207).json({ // 207 Multi-Status
                message: `تم استيراد ${successfulRows} من ${employees.length} موظف`,
                total_rows: employees.length,
                successful_imports: successfulRows,
                failed_imports: failedRows.length,
                errors: failedRows
            });
        }
    } catch (error) {
        console.error('خطأ عام في استيراد البيانات:', error);
        res.status(500).json({
            error: 'حدث خطأ أثناء استيراد البيانات',
            details: error.message
        });
    }
});

// إنشاء جدول الإجازات إذا لم يكن موجوداً
// تم نقل جميع maintenance endpoints إلى routes/maintenance.js

// الحصول على جميع الإدارات الفريدة
app.get('/api/departments', async (req, res) => {
  try {
    const { include_resigned } = req.query;
    let query = 'SELECT DISTINCT department FROM employees WHERE department IS NOT NULL';

    // إضافة شرط الحالة (نشط فقط إلا إذا طُلب تضمين المستقيلين)
    if (include_resigned !== 'true') {
      query += " AND status = 'نشط'";
    }

    query += ' ORDER BY department';

    const [rows] = await pool.promise().query(query);

    // استخراج أسماء الإدارات فقط كمصفوفة
    const departments = rows.map(row => row.department);

    res.json(departments);
  } catch (error) {
    console.error('Error fetching departments:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات الإدارات' });
  }
});

// تم نقل جميع endpoints الإجازات إلى routes/vacations.js

// Get all employees - تم نقله إلى routes/employees.js

// Search employees - تم نقله إلى routes/employees.js

// Get single employee - تم نقله إلى routes/employees.js
// Get next employee code - تم نقله إلى routes/employees.js
// Add new employee - تم نقله إلى routes/employees.js
// Update employee - تم نقله إلى routes/employees.js
// Delete employee - تم نقله إلى routes/employees.js
// Delete all employees - تم نقله إلى routes/employees.js
// جميع endpoints الموظفين تم نقلها إلى routes/employees.js

// تم نقل جميع maintenance endpoints إلى routes/maintenance.js

// تم نقل جميع endpoints الإجازات إلى routes/vacations.js

// تم نقل endpoint status إلى routes/utilities.js

// إضافة إجازة جديدة
app.post('/api/vacations', authenticateToken, async (req, res) => {
  try {
    const { employee_code, department, vacation_type, official_type, vacation_date, start_date, end_date, days_count } = req.body;
    
    // التحقق من البيانات المطلوبة
    if (!employee_code || !department || !vacation_type || !vacation_date) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب تعبئتها' });
    }
    
    // الحصول على اسم الموظف والتحقق من حالته
    const [employeeResult] = await pool.promise().query(
      'SELECT full_name, status FROM employees WHERE code = ?',
      [employee_code]
    );
    
    if (employeeResult.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    // التحقق من أن الموظف نشط
    if (employeeResult[0].status === 'مستقيل') {
      return res.status(400).json({ error: 'لا يمكن إضافة إجازة لموظف مستقيل' });
    }

    const employeeName = employeeResult[0].full_name;

    // حساب عدد الأيام تلقائياً إذا تم توفير تاريخ البداية والنهاية
    let calculatedDaysCount = days_count || 1;
    let finalStartDate = start_date || vacation_date;
    let finalEndDate = end_date || vacation_date;

    if (start_date && end_date) {
      const startDateObj = new Date(start_date);
      const endDateObj = new Date(end_date);

      if (endDateObj >= startDateObj) {
        const diffTime = endDateObj.getTime() - startDateObj.getTime();
        calculatedDaysCount = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 لتضمين يوم البداية
      }

      finalStartDate = start_date;
      finalEndDate = end_date;
    }

    // التحقق من عدم وجود إجازة مكررة في نفس التاريخ
    const [existingVacations] = await pool.promise().query(
      `SELECT id, vacation_type, vacation_date
       FROM vacations
       WHERE employee_code = ? AND vacation_date = ?`,
      [employee_code, vacation_date]
    );

    if (existingVacations.length > 0) {
      const existing = existingVacations[0];

      return res.status(400).json({
        error: `يوجد إجازة أخرى للموظف ${employeeName} في تاريخ ${existing.vacation_date}. لا يمكن إضافة إجازات مكررة.`,
        existing_vacation: {
          id: existing.id,
          type: existing.vacation_type,
          vacation_date: existing.vacation_date
        }
      });
    }

    // التحقق من وجود الأعمدة المطلوبة في جدول vacations
    try {
      await pool.promise().query("SELECT employee_name FROM vacations LIMIT 1");
    } catch (columnError) {
      await pool.promise().query("ALTER TABLE vacations ADD COLUMN employee_name VARCHAR(255) AFTER employee_code");
    }

    // إدراج سجل منفصل لكل يوم من أيام الإجازة
    const startDateObj = new Date(finalStartDate);
    let currentDate = new Date(startDateObj);
    let workDaysAdded = 0;
    const insertedIds = [];

    while (workDaysAdded < calculatedDaysCount) {
      if (currentDate.getDay() !== 5) { // تخطي أيام الجمعة
        const sql = `
          INSERT INTO vacations (
            employee_code, employee_name, department, vacation_type,
            official_type, vacation_date
          ) VALUES (?, ?, ?, ?, ?, ?)
        `;

        const values = [
          employee_code,
          employeeName,
          department,
          vacation_type,
          official_type || null,
          currentDate.toISOString().split('T')[0] // vacation_date = التاريخ الحالي
        ];

        const [result] = await pool.promise().query(sql, values);
        insertedIds.push(result.insertId);
        workDaysAdded++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // تحديث رصيد الإجازات المستخدمة للموظف إذا لم تكن إجازة رسمية
    if (vacation_type !== 'official') {
      try {
        // استدعاء وظيفة حساب رصيد الإجازات تلقائياً بعد إضافة إجازة جديدة
        // الحصول على جميع الإجازات للموظف (باستثناء الإجازات الرسمية)
        const [vacations] = await pool.promise().query(
          'SELECT vacation_type FROM vacations WHERE employee_code = ? AND vacation_type != "official"',
          [employee_code]
        );
        
        // الحصول على بيانات الموظف الحالية
        const [employeeData] = await pool.promise().query(
          'SELECT leave_balance, full_name FROM employees WHERE code = ?',
          [employee_code]
        );
        
        if (employeeData.length > 0) {
          // حساب مجموع أيام الإجازات المستخدمة
          let totalUsedDays = 0;
          
          // حساب مجموع أيام الإجازات (باستثناء الإجازات الرسمية)
          // كل سجل في الجدول يمثل يوم واحد
          totalUsedDays = vacations.length;
          
          // الحصول على رصيد الإجازات الكلي
          const leaveBalance = parseInt(employeeData[0].leave_balance) || 0;
          
          // حساب الإجازات المتبقية
          const leaveRemaining = Math.max(0, leaveBalance - totalUsedDays);
          
          // تحديث حقول leave_used و leave_remaining في جدول الموظفين
          await pool.promise().query(
            'UPDATE employees SET leave_used = ?, leave_remaining = ? WHERE code = ?',
            [totalUsedDays, leaveRemaining, employee_code]
          );
          
          const employeeName = employeeData[0].full_name || employee_name;

        }
      } catch (updateError) {
        console.error(`خطأ في تحديث رصيد الإجازات للموظف ${employee_code}:`, updateError);
        // استمر في إضافة الإجازة حتى لو فشل تحديث الرصيد
      }
    }
    
    // تسجيل النشاط لكل يوم مضاف
    for (const insertId of insertedIds) {
      await logAction({
        user_id: req.user?.id || null,
        username: req.user?.username || 'مجهول',
        action_type: 'add',
        module: 'vacations',
        record_id: insertId.toString(),
        message: `تم إضافة إجازة ${getVacationTypeLabel(vacation_type)} للموظف: ${employeeName} (كود: ${employee_code}) - التاريخ: ${finalStartDate}`
      });
    }

    res.status(201).json({
      message: `تم إضافة الإجازة بنجاح (${calculatedDaysCount} ${calculatedDaysCount === 1 ? 'يوم' : 'أيام'}) وتحديث رصيد الإجازات`,
      id: insertedIds[0], // إرجاع أول ID
      vacation: {
        employee_code,
        employee_name: employeeName,
        vacation_type,
        vacation_date: finalStartDate,
        total_records: insertedIds.length
      }
    });
  } catch (error) {
    console.error('Error adding vacation:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء إضافة الإجازة' });
  }
});

// تم نقل start/stop endpoints إلى routes/maintenance.js

// حساب رصيد الإجازات لموظف واحد
app.get('/api/employees/:code/leave-balance', async (req, res) => {
  try {
    const employeeCode = req.params.code;

    // الحصول على بيانات الموظف (بما في ذلك المستقيلين)
    let [employees] = await pool.promise().query(
      'SELECT code, full_name, hire_date, leave_balance, leave_used, leave_remaining FROM employees WHERE code = ?',
      [employeeCode]
    );

    // إذا لم يوجد في جدول الموظفين، ابحث في جدول الاستقالات
    if (employees.length === 0) {
      const [resignedEmployees] = await pool.promise().query(`
        SELECT e.code, e.full_name, e.hire_date, e.leave_balance, e.leave_used, e.leave_remaining
        FROM resignations r
        LEFT JOIN employees e ON r.employee_code = e.code
        WHERE r.employee_code = ?
      `, [employeeCode]);

      if (resignedEmployees.length === 0) {
        return res.status(404).json({ error: 'الموظف غير موجود' });
      }

      employees = resignedEmployees;
    }

    const employee = employees[0];

    // حساب رصيد الإجازات بناءً على تاريخ التعيين
    let calculatedBalance = 7; // قيمة افتراضية

    if (employee.hire_date) {
      try {
        let hireDate = new Date(employee.hire_date);

        // معالجة التواريخ بصيغ مختلفة
        if (typeof employee.hire_date === 'string' && employee.hire_date.includes('/')) {
          const parts = employee.hire_date.split('/');
          if (parts.length === 3) {
            hireDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
          }
        }

        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const referenceDate = new Date(currentYear, 5, 25); // 25 يونيو

        if (currentDate < referenceDate) {
          referenceDate.setFullYear(currentYear - 1);
        }

        if (!isNaN(hireDate.getTime()) && hireDate <= currentDate) {
          const yearDiff = referenceDate.getFullYear() - hireDate.getFullYear();
          const monthDiff = referenceDate.getMonth() - hireDate.getMonth();
          const dayDiff = referenceDate.getDate() - hireDate.getDate();

          let yearsOfService = yearDiff;
          if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
            yearsOfService--;
          }

          const totalMonths = yearDiff * 12 + monthDiff;
          const monthsOfService = totalMonths >= 0 ? totalMonths : 0;

          if (monthsOfService < 6) {
            calculatedBalance = 7;
          } else if (yearsOfService < 10) {
            calculatedBalance = 21;
          } else {
            calculatedBalance = 30;
          }
        }
      } catch (error) {
        console.error('خطأ في حساب رصيد الإجازات:', error);
      }
    }

    // حساب الإجازات المستخدمة من قاعدة البيانات (عد السجلات)
    const [vacations] = await pool.promise().query(
      'SELECT COUNT(*) as total_used FROM vacations WHERE employee_code = ? AND vacation_type != "official"',
      [employeeCode]
    );

    const usedDays = parseInt(vacations[0]?.total_used) || 0;
    const remainingDays = Math.max(0, calculatedBalance - usedDays);

    res.json({
      employee_code: employeeCode,
      employee_name: employee.full_name,
      hire_date: employee.hire_date,
      calculated_balance: calculatedBalance,
      used_days: usedDays,
      remaining_days: remainingDays,
      current_balance: employee.leave_balance,
      current_used: employee.leave_used,
      current_remaining: employee.leave_remaining
    });

  } catch (error) {
    console.error('خطأ في حساب رصيد الإجازات للموظف:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء حساب رصيد الإجازات' });
  }
});

// تم نقل جميع endpoints الإجازات إلى routes/vacations.js

// تم نقل جميع endpoints العهد إلى routes/custody.js

// تم نقل جميع endpoints العهد إلى routes/custody.js





// تم نقل جميع endpoints العهد إلى routes/custody.js





// تشغيل الخادم








// تم نقل جميع endpoints الصيانة إلى routes/maintenance.js

// تم نقل جميع endpoints الصيانة إلى routes/maintenance.js

// تم نقل جميع endpoints إلى routes منفصلة

// عرض صلاحيات المستخدم الحالي
app.get('/api/my-permissions', authenticateToken, async (req, res) => {
  try {
    const [users] = await pool.promise().query(
      "SELECT username, permissions FROM users WHERE id = ?",
      [req.user.id]
    );

    if (users.length === 0) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    const user = users[0];
    let permissions = {};

    if (user.permissions) {
      permissions = typeof user.permissions === 'string'
        ? JSON.parse(user.permissions)
        : user.permissions;
    }

    res.json({
      username: user.username,
      permissions: permissions
    });
  } catch (error) {
    console.error('خطأ في جلب صلاحيات المستخدم:', error);
    res.status(500).json({ error: 'فشل في جلب صلاحيات المستخدم' });
  }
});









// تم نقل جميع endpoints العامل المثالي إلى routes/idealEmployees.js

app.listen(port, '0.0.0.0', () => {
  console.log(`الخادم يعمل على المنفذ ${port}`);
  console.log(`يمكنك الوصول إلى التطبيق على: http://localhost:${port}`);
});