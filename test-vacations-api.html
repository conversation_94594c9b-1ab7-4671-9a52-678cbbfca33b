<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الإجازات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>اختبار API الإجازات</h1>
    
    <div class="test-section">
        <h2>0. تسجيل الدخول</h2>
        <button onclick="testLogin()">تسجيل الدخول كـ admin</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h2>1. إنشاء موظف تجريبي</h2>
        <button onclick="testCreateEmployee()">إنشاء موظف تجريبي</button>
        <div id="createEmployeeResult"></div>
    </div>

    <div class="test-section">
        <h2>2. اختبار إنشاء جدول الإجازات</h2>
        <button onclick="testCreateTable()">إنشاء جدول الإجازات</button>
        <div id="createTableResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. اختبار إضافة إجازة</h2>
        <button onclick="testAddVacation()">إضافة إجازة تجريبية</button>
        <div id="addVacationResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. اختبار جلب الإجازات</h2>
        <button onclick="testGetVacations()">جلب جميع الإجازات</button>
        <div id="getVacationsResult"></div>
    </div>

    <div class="test-section">
        <h2>5. اختبار تحديث إجازة</h2>
        <button onclick="testUpdateVacation()">تحديث إجازة</button>
        <div id="updateVacationResult"></div>
    </div>

    <div class="test-section">
        <h2>6. اختبار حذف إجازة</h2>
        <button onclick="testDeleteVacation()">حذف إجازة</button>
        <div id="deleteVacationResult"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5500';
        let testVacationId = null;
        
        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            const resultClass = success ? 'success' : 'error';
            let content = `<div class="test-result ${resultClass}">${message}</div>`;

            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }

            element.innerHTML = content;
        }

        async function testLogin() {
            try {
                const loginData = {
                    username: 'admin',
                    password: '123456'
                };

                const response = await fetch(`${API_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('token', data.token);
                    showResult('loginResult', true, 'تم تسجيل الدخول بنجاح', { token: data.token.substring(0, 20) + '...' });
                } else {
                    const error = await response.json();
                    showResult('loginResult', false, `فشل في تسجيل الدخول: ${response.status}`, error);
                }
            } catch (error) {
                showResult('loginResult', false, `خطأ في الاتصال: ${error.message}`);
            }
        }

        async function testCreateEmployee() {
            try {
                const employeeData = {
                    code: '12345',
                    full_name: 'أحمد محمد علي',
                    department: 'تقنية المعلومات',
                    position: 'مطور',
                    hire_date: '2024-01-01',
                    salary: 5000,
                    phone: '01234567890',
                    email: '<EMAIL>'
                };

                const response = await fetch(`${API_URL}/api/employees`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify(employeeData)
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('createEmployeeResult', true, 'تم إنشاء الموظف التجريبي بنجاح', data);
                } else {
                    const error = await response.json();
                    showResult('createEmployeeResult', false, `فشل في إنشاء الموظف: ${response.status}`, error);
                }
            } catch (error) {
                showResult('createEmployeeResult', false, `خطأ في الاتصال: ${error.message}`);
            }
        }

        async function testCreateTable() {
            try {
                const response = await fetch(`${API_URL}/api/vacations/setup-vacations-table`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('createTableResult', true, 'تم إنشاء جدول الإجازات بنجاح', data);
                } else {
                    const error = await response.text();
                    showResult('createTableResult', false, `فشل في إنشاء الجدول: ${response.status}`, error);
                }
            } catch (error) {
                showResult('createTableResult', false, `خطأ في الاتصال: ${error.message}`);
            }
        }
        
        async function testAddVacation() {
            try {
                const vacationData = {
                    employee_code: '12345',
                    vacation_type: 'annual',
                    vacation_date: '2024-01-15',
                    days_count: 1,
                    official_type: null
                };
                
                const response = await fetch(`${API_URL}/api/vacations`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify(vacationData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    testVacationId = data.id;
                    showResult('addVacationResult', true, 'تم إضافة الإجازة بنجاح', data);
                } else {
                    const error = await response.json();
                    showResult('addVacationResult', false, `فشل في إضافة الإجازة: ${response.status}`, error);
                }
            } catch (error) {
                showResult('addVacationResult', false, `خطأ في الاتصال: ${error.message}`);
            }
        }
        
        async function testGetVacations() {
            try {
                const response = await fetch(`${API_URL}/api/vacations`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('getVacationsResult', true, `تم جلب ${data.length} إجازة`, data);
                } else {
                    const error = await response.text();
                    showResult('getVacationsResult', false, `فشل في جلب الإجازات: ${response.status}`, error);
                }
            } catch (error) {
                showResult('getVacationsResult', false, `خطأ في الاتصال: ${error.message}`);
            }
        }
        
        async function testUpdateVacation() {
            if (!testVacationId) {
                showResult('updateVacationResult', false, 'يجب إضافة إجازة أولاً');
                return;
            }
            
            try {
                const updateData = {
                    vacation_type: 'sick',
                    days_count: 2
                };
                
                const response = await fetch(`${API_URL}/api/vacations/${testVacationId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify(updateData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('updateVacationResult', true, 'تم تحديث الإجازة بنجاح', data);
                } else {
                    const error = await response.json();
                    showResult('updateVacationResult', false, `فشل في تحديث الإجازة: ${response.status}`, error);
                }
            } catch (error) {
                showResult('updateVacationResult', false, `خطأ في الاتصال: ${error.message}`);
            }
        }
        
        async function testDeleteVacation() {
            if (!testVacationId) {
                showResult('deleteVacationResult', false, 'يجب إضافة إجازة أولاً');
                return;
            }
            
            try {
                const response = await fetch(`${API_URL}/api/vacations/${testVacationId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('deleteVacationResult', true, 'تم حذف الإجازة بنجاح', data);
                    testVacationId = null;
                } else {
                    const error = await response.json();
                    showResult('deleteVacationResult', false, `فشل في حذف الإجازة: ${response.status}`, error);
                }
            } catch (error) {
                showResult('deleteVacationResult', false, `خطأ في الاتصال: ${error.message}`);
            }
        }
    </script>
</body>
</html>
