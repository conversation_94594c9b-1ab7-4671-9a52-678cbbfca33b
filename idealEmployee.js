// ملف JavaScript للعامل المثالي

// متغيرات عامة - استخدام config.js والحالة العامة
let API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || "http://localhost:5500/api");
let employees = window.GlobalState ? window.GlobalState.employees : [];
let idealEmployees = window.GlobalState ? window.GlobalState.idealEmployees : [];
let currentEditId = window.GlobalState ? window.GlobalState.currentEditId : null;

// عناصر DOM
const employeeSearchAdd = document.getElementById('employeeSearchAdd');
const employeeCode = document.getElementById('employeeCode');
const employeeName = document.getElementById('employeeName');
const employeeDepartment = document.getElementById('employeeDepartment');
const fromPeriod = document.getElementById('fromPeriod');
const toPeriod = document.getElementById('toPeriod');
const evaluationScore = document.getElementById('evaluationScore');
const rewardAmount = document.getElementById('rewardAmount');
const selectionReason = document.getElementById('selectionReason');
const notes = document.getElementById('notes');

// اختبار الاتصال بـ API
async function testAPIConnection() {
  try {
    const response = await fetch(`${API_URL}/ideal-employees/test`);
    if (response.ok) {
      const data = await response.json();
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

// دالة اختبار الصلاحيات (للاستخدام في وحدة التحكم)
window.testIdealEmployeePermissions = function() {
  const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');

  const idealEmployeePermissions = [
    'view_ideal_employee',
    'add_ideal_employee',
    'edit_ideal_employee',
    'delete_ideal_employee',
    'view_ideal_employee_reports',
    'export_ideal_employee_data'
  ];

  idealEmployeePermissions.forEach(permission => {
    const hasPermission = permissions[permission] === true;
    console.log(`${hasPermission ? '✅' : '❌'} ${permission}: ${hasPermission}`);
  });

  // فحص عنصر القائمة الجانبية
  const menuItem = document.querySelector('[data-section="ideal-employee"]');
  if (menuItem) {
    const parentItem = menuItem.closest('.menu-item');
    // فحص حالة عنصر القائمة الجانبية
  }
};

// دالة التهيئة الرئيسية
function initializeIdealEmployeeJS() {
  // إضافة أنماط الإشعارات
  addNotificationStyles();

  // اختبار الاتصال بـ API أولاً
  testAPIConnection().then(isConnected => {

    // تحميل البيانات
    loadEmployees();
    loadIdealEmployees();
  });

  // إعداد الأحداث
  setupEventListeners();

  // تحميل الإدارات في فلاتر التقارير
  loadDepartmentsForReports();

  // التحقق من المحتوى المحدد من البطاقات
  checkSelectedContent();

  // التأكد من عرض محتوى افتراضي إذا لم يتم عرض أي محتوى
  setTimeout(() => {
    const visibleContent = document.querySelector('.tab-content.active');
    if (!visibleContent) {
      showContent('add-ideal-employee');
    }
  }, 200);

  // إعداد الميزات الإضافية
  setupAdditionalFeatures();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
  // البحث عن الموظف
  if (employeeSearchAdd) {
    employeeSearchAdd.addEventListener('input', function() {
      handleEmployeeSearch(this.value, 'employeeSearchSuggestions');
    });

    // مستمع للتغيير عند اختيار من القائمة المنسدلة
    employeeSearchAdd.addEventListener('change', function() {
      handleEmployeeSelection(this.value);
    });

    // مستمع للنقر على خيار من datalist
    employeeSearchAdd.addEventListener('blur', function() {
      // تأخير قصير للسماح بمعالجة الاختيار
      setTimeout(() => {
        if (this.value && this.value !== "لا توجد نتائج مطابقة") {
          handleEmployeeSelection(this.value);
        }
      }, 100);
    });
  }
  
  // نموذج إضافة العامل المثالي
  const idealEmployeeForm = document.getElementById('idealEmployeeForm');
  if (idealEmployeeForm) {
    idealEmployeeForm.addEventListener('submit', saveIdealEmployee);
  }
  
  // زر إعادة التعيين
  const resetFormBtn = document.getElementById('resetForm');
  if (resetFormBtn) {
    resetFormBtn.addEventListener('click', resetForm);
  }
  
  // زر إعادة تحميل البيانات
  const reloadBtn = document.getElementById('reloadIdealEmployees');
  if (reloadBtn) {
    reloadBtn.addEventListener('click', function() {
      console.log('🔄 إعادة تحميل البيانات يدوياً...');
      loadEmployees();
      loadIdealEmployees();

      // إعادة تطبيق إصلاح القائمة الجانبية
      if (typeof fixSidebarDisplay === 'function') {
        fixSidebarDisplay();
      }

      // إعادة تطبيق الصلاحيات للتأكد من ظهور القائمة
      if (typeof sidebarPermissionManager !== 'undefined' && sidebarPermissionManager) {
        sidebarPermissionManager.applyPermissions();
      }

      showNotification('تم إعادة تحميل البيانات', 'info');
    });
  }

  // زر تصدير العمال المثاليين
  const exportBtn = document.getElementById('exportIdealEmployees');
  if (exportBtn) {
    exportBtn.addEventListener('click', exportIdealEmployeesToExcel);
  }
  
  // أزرار التقارير
  const generateReportBtn = document.getElementById('generateReport');
  if (generateReportBtn) {
    generateReportBtn.addEventListener('click', generateReport);
  }
  
  const resetFiltersBtn = document.getElementById('resetFilters');
  if (resetFiltersBtn) {
    resetFiltersBtn.addEventListener('click', resetReportFilters);
  }
  
  const printReportBtn = document.getElementById('printReport');
  if (printReportBtn) {
    printReportBtn.addEventListener('click', printReport);
  }
  
  const exportReportBtn = document.getElementById('exportReport');
  if (exportReportBtn) {
    exportReportBtn.addEventListener('click', exportReportToExcel);
  }
  
  // Modal events
  setupModalEvents();
}



// تحميل الموظفين
async function loadEmployees() {
  try {

    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      employees = await response.json();

    } else {

      // لا نعرض إشعار خطأ إذا كان السيرفر غير متصل
      if (response.status !== 404) {
        showNotification('فشل في تحميل بيانات الموظفين', 'error');
      }
    }
  } catch (error) {
    // لا نعرض إشعار خطأ إذا كان السيرفر غير متصل
    // السيرفر غير متصل - سيتم تحميل البيانات عند تشغيل السيرفر
  }
}

// تحميل العمال المثاليين
async function loadIdealEmployees() {
  try {

    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/ideal-employees`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });


    if (response.ok) {
      idealEmployees = await response.json();


      // عرض البيانات في جدول الإضافة
      displayIdealEmployees();

      // تحديث جدول التقارير أيضاً إذا كان موجوداً
      const reportTableBody = document.getElementById('reportTableBody');
      if (reportTableBody) {
        console.log('📊 تحديث جدول التقارير أيضاً...');
        displayReportResults(idealEmployees);
        updateReportStatistics(idealEmployees);
      }
    } else {
      console.error('❌ فشل في تحميل العمال المثاليين - رمز الاستجابة:', response.status);

      // محاولة قراءة رسالة الخطأ
      try {
        const errorData = await response.text();
        console.error('تفاصيل الخطأ:', errorData);
      } catch (e) {
        console.error('لا يمكن قراءة تفاصيل الخطأ');
      }

      // عرض جدول فارغ
      idealEmployees = [];
      displayIdealEmployees();

      // تحديث جدول التقارير أيضاً
      const reportTableBody = document.getElementById('reportTableBody');
      if (reportTableBody) {
        displayReportResults([]);
        updateReportStatistics([]);
      }

      if (response.status === 404) {
        console.log('⚠️ endpoint غير موجود - تأكد من إعادة تشغيل السيرفر');
      }
    }
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error);
    // عرض جدول فارغ
    idealEmployees = [];
    displayIdealEmployees();

    // تحديث جدول التقارير أيضاً
    const reportTableBody = document.getElementById('reportTableBody');
    if (reportTableBody) {
      displayReportResults([]);
      updateReportStatistics([]);
    }

    console.log('⚠️ لا يمكن الاتصال بالسيرفر - تأكد من تشغيل السيرفر');
  }
}

// البحث عن الموظف
function handleEmployeeSearch(searchTerm, datalistId) {
  const datalist = document.getElementById(datalistId);
  if (!datalist) return;

  datalist.innerHTML = '';

  console.log(`البحث عن الموظف: ${searchTerm}, عدد الموظفين المتاحين: ${employees.length}`);

  if (!searchTerm || searchTerm.trim() === '') {
    // مسح الحقول عند مسح البحث
    clearEmployeeFields();
    return;
  }

  const searchTermLower = searchTerm.toLowerCase().trim();

  // البحث في الموظفين - تحسين البحث ليشمل أجزاء من الاسم
  const filteredEmployees = employees.filter(emp => {
    // التأكد من أن البيانات موجودة وتحويلها إلى نص
    const fullName = emp.full_name ? String(emp.full_name).toLowerCase() : '';
    const code = emp.code ? String(emp.code).toLowerCase() : '';
    const department = emp.department ? String(emp.department).toLowerCase() : '';

    // البحث في كل الحقول
    return fullName.includes(searchTermLower) ||
           code.includes(searchTermLower) ||
           department.includes(searchTermLower);
  });

  console.log(`نتائج البحث: ${filteredEmployees.length} موظف`);
  if (filteredEmployees.length > 0) {
    console.log('أول نتيجة:', filteredEmployees[0]);
  }

  // البحث عن تطابق مباشر أولاً
  const exactCodeMatch = employees.find(emp =>
    emp.code && String(emp.code).toLowerCase() === searchTermLower
  );
  const exactNameMatch = employees.find(emp =>
    emp.full_name && String(emp.full_name).toLowerCase() === searchTermLower
  );

  // إذا وُجد تطابق مباشر، املأ الحقول
  if (exactCodeMatch) {
    fillEmployeeFields(exactCodeMatch);
  } else if (exactNameMatch) {
    fillEmployeeFields(exactNameMatch);
  }
  // إذا كان هناك موظف واحد فقط في النتائج، املأ الحقول
  else if (filteredEmployees.length === 1) {
    fillEmployeeFields(filteredEmployees[0]);
  }
  // إذا كان البحث يبدأ بكود أو اسم موظف، املأ الحقول
  else {
    const startsWithMatch = employees.find(emp => {
      const nameLower = emp.full_name ? String(emp.full_name).toLowerCase() : '';
      const codeLower = emp.code ? String(emp.code).toLowerCase() : '';
      return nameLower.startsWith(searchTermLower) || codeLower.startsWith(searchTermLower);
    });

    if (startsWithMatch) {
      fillEmployeeFields(startsWithMatch);
    }
  }

  // إضافة الخيارات للقائمة المنسدلة - تحسين عرض الاقتراحات
  if (filteredEmployees.length > 0) {
    // ترتيب النتائج: الأكواد أولاً ثم الأسماء
    filteredEmployees.sort((a, b) => {
      // إذا كان البحث يطابق الكود، ضع هذا الموظف في المقدمة
      const aCodeMatch = a.code && String(a.code).toLowerCase().includes(searchTermLower);
      const bCodeMatch = b.code && String(b.code).toLowerCase().includes(searchTermLower);

      if (aCodeMatch && !bCodeMatch) return -1;
      if (!aCodeMatch && bCodeMatch) return 1;

      // ثم رتب حسب الاسم
      return (a.full_name || '').localeCompare(b.full_name || '');
    });

    // عرض أول 15 نتيجة فقط لتحسين الأداء
    filteredEmployees.slice(0, 15).forEach(emp => {
      const option = document.createElement('option');
      option.value = `${emp.code} - ${emp.full_name}`;
      datalist.appendChild(option);
    });
  } else {
    // إضافة رسالة إذا لم يتم العثور على نتائج
    const option = document.createElement('option');
    option.value = "لا توجد نتائج مطابقة";
    datalist.appendChild(option);
  }
}

// التعامل مع اختيار الموظف
function handleEmployeeSelection(selectedValue) {
  console.log('تم اختيار الموظف:', selectedValue);

  if (!selectedValue || selectedValue === "لا توجد نتائج مطابقة") {
    clearEmployeeFields();
    return;
  }

  // استخراج كود الموظف من القيمة المختارة
  const parts = selectedValue.split(' - ');
  if (parts.length >= 2) {
    const employeeCode = parts[0].trim();
    const employee = employees.find(emp => String(emp.code) === employeeCode);

    if (employee) {
      console.log('تم العثور على الموظف:', employee);
      fillEmployeeFields(employee);
    } else {
      console.log('لم يتم العثور على الموظف بالكود:', employeeCode);
    }
  } else {
    // إذا لم يكن التنسيق صحيحاً، جرب البحث المباشر
    const employee = employees.find(emp =>
      emp.full_name === selectedValue || emp.code === selectedValue
    );

    if (employee) {
      fillEmployeeFields(employee);
    }
  }
}

// ملء حقول الموظف
function fillEmployeeFields(employee) {
  if (employeeCode) employeeCode.value = employee.code;
  if (employeeName) employeeName.value = employee.full_name;
  if (employeeDepartment) employeeDepartment.value = employee.department;
}

// مسح حقول الموظف
function clearEmployeeFields() {
  if (employeeCode) employeeCode.value = '';
  if (employeeName) employeeName.value = '';
  if (employeeDepartment) employeeDepartment.value = '';
}

// حفظ العامل المثالي
async function saveIdealEmployee(event) {
  event.preventDefault();
  
  if (!validateForm()) {
    return;
  }
  
  const idealEmployeeData = {
    employee_code: employeeCode.value,
    employee_name: employeeName.value,
    department: employeeDepartment.value,
    from_period: fromPeriod.value,
    to_period: toPeriod.value,
    evaluation_score: parseFloat(evaluationScore.value),
    reward_amount: parseFloat(rewardAmount.value),
    selection_reason: selectionReason.value,
    notes: notes.value || null
  };
  
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/ideal-employees`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(idealEmployeeData)
    });
    
    if (response.ok) {
      showNotification('تم حفظ العامل المثالي بنجاح', 'success');
      resetForm();
      loadIdealEmployees();
    } else {
      const error = await response.json();
      showNotification(error.message || 'فشل في حفظ العامل المثالي', 'error');
    }
  } catch (error) {
    console.error('خطأ في حفظ العامل المثالي:', error);
    showNotification('خطأ في الاتصال بالخادم', 'error');
  }
}

// التحقق من صحة النموذج
function validateForm() {
  if (!employeeCode.value) {
    showNotification('يرجى اختيار موظف', 'error');
    employeeSearchAdd.focus();
    return false;
  }

  if (!fromPeriod.value || !toPeriod.value) {
    showNotification('يرجى تحديد فترة التقييم', 'error');
    fromPeriod.focus();
    return false;
  }

  if (new Date(fromPeriod.value) >= new Date(toPeriod.value)) {
    showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
    fromPeriod.focus();
    return false;
  }

  if (!evaluationScore.value || evaluationScore.value < 0 || evaluationScore.value > 100) {
    showNotification('يرجى إدخال درجة تقييم صحيحة (0-100)', 'error');
    evaluationScore.focus();
    return false;
  }

  if (!rewardAmount.value || rewardAmount.value < 0) {
    showNotification('يرجى إدخال مبلغ مكافأة صحيح', 'error');
    rewardAmount.focus();
    return false;
  }

  if (!selectionReason.value.trim()) {
    showNotification('يرجى إدخال سبب اختيار الموظف', 'error');
    selectionReason.focus();
    return false;
  }

  return true;
}

// إعادة تعيين النموذج
function resetForm() {
  document.getElementById('idealEmployeeForm').reset();
  clearEmployeeFields();
  if (employeeSearchAdd) employeeSearchAdd.value = '';
  
  // مسح قائمة الاقتراحات
  const datalist = document.getElementById('employeeSearchSuggestions');
  if (datalist) {
    datalist.innerHTML = '';
  }
}

// عرض العمال المثاليين
function displayIdealEmployees() {
  const tableBody = document.getElementById('idealEmployeesTableBody');
  if (!tableBody) return;
  
  tableBody.innerHTML = '';
  
  idealEmployees.forEach(idealEmployee => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${idealEmployee.employee_code}</td>
      <td>${idealEmployee.employee_name}</td>
      <td>${idealEmployee.department}</td>
      <td>${formatDate(idealEmployee.from_period)}</td>
      <td>${formatDate(idealEmployee.to_period)}</td>
      <td>${formatAmount(idealEmployee.evaluation_score)}</td>
      <td>${formatAmount(idealEmployee.reward_amount)}</td>
      <td>${idealEmployee.selection_reason}</td>
      <td>
        ${hasPermission('edit_ideal_employee') ? `<button class="action-btn edit-btn" onclick="editIdealEmployee(${idealEmployee.id})">
          <i class="fas fa-edit"></i> تعديل
        </button>` : ''}
        ${hasPermission('delete_ideal_employee') ? `<button class="action-btn delete-btn" onclick="deleteIdealEmployee(${idealEmployee.id})">
          <i class="fas fa-trash"></i> حذف
        </button>` : ''}
      </td>
    `;
    tableBody.appendChild(row);
  });
  
  // تطبيق الصلاحيات
  if (typeof PermissionManager !== 'undefined') {
    const permissionManager = new PermissionManager();
    permissionManager.applyPermissions();
  }

  // تحديث جدول التقارير أيضاً إذا كان موجوداً
  const reportTableBody = document.getElementById('reportTableBody');
  if (reportTableBody) {
    displayReportResults(idealEmployees);
    updateReportStatistics(idealEmployees);
  }
}

// تنسيق التاريخ
function formatDate(dateString) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  // عرض التاريخ بالتنسيق الميلادي
  return date.toLocaleDateString('en-GB'); // DD/MM/YYYY
}

// تنسيق المبلغ
function formatAmount(amount) {
  if (!amount) return '0';
  const num = parseFloat(amount);
  // إذا كان الرقم صحيح (بدون كسور) اعرضه بدون .00
  if (num % 1 === 0) {
    return num.toString();
  }
  // إذا كان له كسور اعرضه بالكسور
  return num.toFixed(2);
}

// تحويل التاريخ للتنسيق المطلوب للـ input (مع إصلاح مشكلة نقص يوم)
function formatDateForInput(dateString) {
  if (!dateString) return '';
  // إصلاح مشكلة التاريخ الناقص يوم - نفس الحل المستخدم في قسم السلف
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const formattedDate = `${year}-${month}-${day}`;

  console.log(`📅 تحويل التاريخ: ${dateString} → ${formattedDate}`);
  return formattedDate;
}

// استخدام دالة showNotification من shared-utils.js

// إضافة أنماط CSS للإشعارات إذا لم تكن موجودة
function addNotificationStyles() {
  if (!document.getElementById('notification-styles')) {
    const style = document.createElement('style');
    style.id = 'notification-styles';
    style.textContent = `
      @keyframes slideOut {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
}

// تصدير العمال المثاليين إلى Excel
function exportIdealEmployeesToExcel() {
  if (idealEmployees.length === 0) {
    showNotification('لا توجد بيانات للتصدير', 'error');
    return;
  }
  
  const data = idealEmployees.map(idealEmployee => ({
    'كود الموظف': idealEmployee.employee_code,
    'الاسم': idealEmployee.employee_name,
    'الإدارة': idealEmployee.department,
    'من فترة': formatDate(idealEmployee.from_period),
    'إلى فترة': formatDate(idealEmployee.to_period),
    'درجة التقييم': idealEmployee.evaluation_score,
    'مبلغ المكافأة': idealEmployee.reward_amount,
    'سبب الاختيار': idealEmployee.selection_reason,
    'ملاحظات': idealEmployee.notes || '',
    'تاريخ الإضافة': formatDate(idealEmployee.created_at)
  }));
  
  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'العمال المثاليين');
  
  const fileName = `العمال_المثاليين_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(wb, fileName);
  
  showNotification('تم تصدير البيانات بنجاح', 'success');
}

// فحص الصلاحيات
function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
    const result = permissions[permission] === true;
    console.log(`[IdealEmployee] hasPermission(${permission}) = ${result}`, permissions);
    return result;
  } catch (error) {
    console.error('خطأ في قراءة الصلاحيات:', error);
    return false;
  }
}

// تعديل العامل المثالي
function editIdealEmployee(id) {
  // فحص صلاحية التعديل
  if (!hasPermission('edit_ideal_employee')) {
    alert('ليس لديك صلاحية لتعديل العامل المثالي');
    return;
  }

  const idealEmployee = idealEmployees.find(emp => emp.id === id);
  if (!idealEmployee) {
    showNotification('لم يتم العثور على العامل المثالي', 'error');
    return;
  }

  currentEditId = id;

  // ملء بيانات Modal
  document.getElementById('editId').value = idealEmployee.id;
  document.getElementById('editEmployeeCode').value = idealEmployee.employee_code;
  document.getElementById('editEmployeeName').value = idealEmployee.employee_name;
  // استخدام formatDateForInput لإصلاح مشكلة نقص يوم في التاريخ
  document.getElementById('editFromPeriod').value = formatDateForInput(idealEmployee.from_period);
  document.getElementById('editToPeriod').value = formatDateForInput(idealEmployee.to_period);
  document.getElementById('editEvaluationScore').value = idealEmployee.evaluation_score;
  document.getElementById('editRewardAmount').value = idealEmployee.reward_amount;
  document.getElementById('editSelectionReason').value = idealEmployee.selection_reason;
  document.getElementById('editNotes').value = idealEmployee.notes || '';

  // إظهار Modal
  document.getElementById('editIdealEmployeeModal').style.display = 'flex';
}

// حذف العامل المثالي
async function deleteIdealEmployee(id) {
  // فحص صلاحية الحذف
  if (!hasPermission('delete_ideal_employee')) {
    alert('ليس لديك صلاحية لحذف العامل المثالي');
    return;
  }

  if (!confirm('هل أنت متأكد من حذف هذا العامل المثالي؟')) {
    return;
  }

  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/ideal-employees/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      showNotification('تم حذف العامل المثالي بنجاح', 'success');
      loadIdealEmployees();
    } else {
      const error = await response.json();
      showNotification(error.message || 'فشل في حذف العامل المثالي', 'error');
    }
  } catch (error) {
    console.error('خطأ في حذف العامل المثالي:', error);
    showNotification('خطأ في الاتصال بالخادم', 'error');
  }
}

// إغلاق modal التعديل
function closeEditModal() {
  document.getElementById('editIdealEmployeeModal').style.display = 'none';
  currentEditId = null;
}

// إعداد أحداث Modal
function setupModalEvents() {
  const updateBtn = document.getElementById('updateIdealEmployee');

  // تحديث العامل المثالي
  if (updateBtn) {
    updateBtn.addEventListener('click', updateIdealEmployee);
  }
}

// التحقق من صحة بيانات التعديل
function validateEditForm() {
  const fromPeriod = document.getElementById('editFromPeriod').value;
  const toPeriod = document.getElementById('editToPeriod').value;
  const evaluationScore = document.getElementById('editEvaluationScore').value;
  const rewardAmount = document.getElementById('editRewardAmount').value;
  const selectionReason = document.getElementById('editSelectionReason').value;

  if (!fromPeriod || !toPeriod) {
    showNotification('يرجى تحديد فترة التقييم', 'error');
    return false;
  }

  if (new Date(fromPeriod) >= new Date(toPeriod)) {
    showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
    return false;
  }

  if (!evaluationScore || evaluationScore < 0 || evaluationScore > 100) {
    showNotification('يرجى إدخال درجة تقييم صحيحة (0-100)', 'error');
    return false;
  }

  if (!rewardAmount || rewardAmount < 0) {
    showNotification('يرجى إدخال مبلغ مكافأة صحيح', 'error');
    return false;
  }

  if (!selectionReason.trim()) {
    showNotification('يرجى إدخال سبب اختيار الموظف', 'error');
    return false;
  }

  return true;
}

// تحديث العامل المثالي
async function updateIdealEmployee() {
  if (!currentEditId) return;

  if (!validateEditForm()) {
    return;
  }

  // تحويل التواريخ إلى الصيغة الصحيحة (تجنب مشاكل المنطقة الزمنية)
  const fromPeriodValue = document.getElementById('editFromPeriod').value;
  const toPeriodValue = document.getElementById('editToPeriod').value;

  function formatDateSafely(dateString) {
    if (!dateString) return null;
    // استخدام التاريخ مباشرة بدون تحويل لتجنب مشاكل المنطقة الزمنية
    return dateString;
  }

  const updatedData = {
    from_period: formatDateSafely(fromPeriodValue),
    to_period: formatDateSafely(toPeriodValue),
    evaluation_score: parseFloat(document.getElementById('editEvaluationScore').value),
    reward_amount: parseFloat(document.getElementById('editRewardAmount').value),
    selection_reason: document.getElementById('editSelectionReason').value,
    notes: document.getElementById('editNotes').value || null
  };

  console.log('📅 بيانات التحديث مع التواريخ المحولة:', updatedData);

  const updateBtn = document.getElementById('updateIdealEmployee');
  const originalText = updateBtn.textContent;

  try {
    // تعطيل الزر وإظهار حالة التحميل
    updateBtn.disabled = true;
    updateBtn.textContent = 'جاري التحديث...';

    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/ideal-employees/${currentEditId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updatedData)
    });

    if (response.ok) {
      showNotification('تم تحديث العامل المثالي بنجاح', 'success');
      closeEditModal();
      loadIdealEmployees();
    } else {
      const error = await response.json();
      showNotification(error.message || 'فشل في تحديث العامل المثالي', 'error');
    }
  } catch (error) {
    console.error('خطأ في تحديث العامل المثالي:', error);
    showNotification('خطأ في الاتصال بالخادم', 'error');
  } finally {
    // استعادة حالة الزر
    updateBtn.disabled = false;
    updateBtn.textContent = originalText;
  }
}

// تحميل الإدارات لفلاتر التقارير
function loadDepartmentsForReports() {
  const departmentSelect = document.getElementById('reportDepartment');
  if (!departmentSelect) return;

  // الحصول على قائمة فريدة من الإدارات
  const departments = [...new Set(employees.map(emp => emp.department))];

  // مسح الخيارات الحالية (عدا الخيار الافتراضي)
  departmentSelect.innerHTML = '<option value="">جميع الإدارات</option>';

  // إضافة الإدارات
  departments.forEach(dept => {
    if (dept) {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      departmentSelect.appendChild(option);
    }
  });
}

// إنشاء التقرير
async function generateReport() {
  const startDate = document.getElementById('reportStartDate').value;
  const endDate = document.getElementById('reportEndDate').value;
  const department = document.getElementById('reportDepartment').value;
  const employee = document.getElementById('reportEmployee').value;

  let filteredData = [...idealEmployees];

  // تطبيق الفلاتر
  if (startDate) {
    filteredData = filteredData.filter(item =>
      new Date(item.from_period) >= new Date(startDate)
    );
  }

  if (endDate) {
    filteredData = filteredData.filter(item =>
      new Date(item.to_period) <= new Date(endDate)
    );
  }

  if (department) {
    filteredData = filteredData.filter(item =>
      item.department === department
    );
  }

  if (employee) {
    const employeeCode = employee.split(' - ')[0];
    filteredData = filteredData.filter(item =>
      item.employee_code === employeeCode ||
      item.employee_name.toLowerCase().includes(employee.toLowerCase())
    );
  }

  // عرض النتائج
  displayReportResults(filteredData);

  // تحديث الإحصائيات
  updateReportStatistics(filteredData);
}

// عرض نتائج التقرير
function displayReportResults(data) {
  console.log('📊 displayReportResults تم استدعاؤها مع:', data.length, 'سجل');

  const tableBody = document.getElementById('reportTableBody');
  console.log('🎯 جدول التقارير:', tableBody ? 'موجود' : 'غير موجود');

  if (!tableBody) {
    console.error('❌ لم يتم العثور على جدول التقارير (reportTableBody)');
    return;
  }

  tableBody.innerHTML = '';
  console.log('🧹 تم مسح محتوى الجدول');

  if (data.length === 0) {
    console.log('📝 عرض رسالة "لا توجد بيانات"');
    const row = document.createElement('tr');
    row.innerHTML = '<td colspan="10" style="text-align: center; color: #666;">لا توجد بيانات مطابقة للفلاتر المحددة</td>';
    tableBody.appendChild(row);
    return;
  }

  console.log('📋 بدء إضافة الصفوف للجدول...');

  data.forEach((item, index) => {
    console.log(`📄 إضافة صف ${index + 1}:`, item.employee_name);
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${item.employee_code}</td>
      <td>${item.employee_name}</td>
      <td>${item.department}</td>
      <td>${formatDate(item.from_period)}</td>
      <td>${formatDate(item.to_period)}</td>
      <td>${formatAmount(item.evaluation_score)}</td>
      <td>${formatAmount(item.reward_amount)}</td>
      <td>${item.selection_reason}</td>
      <td>${item.notes || '-'}</td>
      <td>${formatDate(item.created_at)}</td>
    `;
    tableBody.appendChild(row);
  });

  console.log('✅ تم إضافة جميع الصفوف للجدول، العدد النهائي:', tableBody.children.length);
}

// تحديث إحصائيات التقرير
function updateReportStatistics(data) {
  console.log('📈 updateReportStatistics تم استدعاؤها مع:', data.length, 'سجل');

  const totalElement = document.getElementById('totalIdealEmployees');
  const totalRewardsElement = document.getElementById('totalRewards');
  const averageScoreElement = document.getElementById('averageScore');

  console.log('🎯 عناصر الإحصائيات:', {
    total: totalElement ? 'موجود' : 'غير موجود',
    rewards: totalRewardsElement ? 'موجود' : 'غير موجود',
    average: averageScoreElement ? 'موجود' : 'غير موجود'
  });

  if (totalElement) {
    totalElement.textContent = data.length;
  }

  if (totalRewardsElement) {
    const totalRewards = data.reduce((sum, item) => sum + parseFloat(item.reward_amount), 0);
    totalRewardsElement.textContent = formatAmount(totalRewards) + ' ريال';
  }

  if (averageScoreElement) {
    if (data.length > 0) {
      const averageScore = data.reduce((sum, item) => sum + parseFloat(item.evaluation_score), 0) / data.length;
      averageScoreElement.textContent = formatAmount(averageScore);
    } else {
      averageScoreElement.textContent = '0';
    }
  }
}

// إعادة تعيين فلاتر التقارير
function resetReportFilters() {
  document.getElementById('reportStartDate').value = '';
  document.getElementById('reportEndDate').value = '';
  document.getElementById('reportDepartment').value = '';
  document.getElementById('reportEmployee').value = '';

  // إعادة إنشاء التقرير
  generateReport();
}

// طباعة التقرير
function printReport() {
  const printContent = document.querySelector('.report-table-container').innerHTML;
  const printWindow = window.open('', '_blank');

  printWindow.document.write(`
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <title>تقرير العامل المثالي</title>
      <style>
        body { font-family: Arial, sans-serif; direction: rtl; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; font-weight: bold; }
        h1 { text-align: center; color: #2c3e50; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <h1>تقرير العامل المثالي</h1>
      <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
      ${printContent}
    </body>
    </html>
  `);

  printWindow.document.close();
  printWindow.print();
}

// تصدير التقرير إلى Excel
function exportReportToExcel() {
  const tableBody = document.getElementById('reportTableBody');
  if (!tableBody || tableBody.children.length === 0) {
    showNotification('لا توجد بيانات للتصدير', 'error');
    return;
  }

  // الحصول على البيانات المفلترة
  const startDate = document.getElementById('reportStartDate').value;
  const endDate = document.getElementById('reportEndDate').value;
  const department = document.getElementById('reportDepartment').value;
  const employee = document.getElementById('reportEmployee').value;

  let filteredData = [...idealEmployees];

  // تطبيق نفس الفلاتر
  if (startDate) {
    filteredData = filteredData.filter(item =>
      new Date(item.from_period) >= new Date(startDate)
    );
  }

  if (endDate) {
    filteredData = filteredData.filter(item =>
      new Date(item.to_period) <= new Date(endDate)
    );
  }

  if (department) {
    filteredData = filteredData.filter(item =>
      item.department === department
    );
  }

  if (employee) {
    const employeeCode = employee.split(' - ')[0];
    filteredData = filteredData.filter(item =>
      item.employee_code === employeeCode ||
      item.employee_name.toLowerCase().includes(employee.toLowerCase())
    );
  }

  const data = filteredData.map(item => ({
    'كود الموظف': item.employee_code,
    'الاسم': item.employee_name,
    'الإدارة': item.department,
    'من فترة': formatDate(item.from_period),
    'إلى فترة': formatDate(item.to_period),
    'درجة التقييم': item.evaluation_score,
    'مبلغ المكافأة': item.reward_amount,
    'سبب الاختيار': item.selection_reason,
    'ملاحظات': item.notes || '',
    'تاريخ الإضافة': formatDate(item.created_at)
  }));

  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'تقرير العامل المثالي');

  const fileName = `تقرير_العامل_المثالي_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(wb, fileName);

  showNotification('تم تصدير التقرير بنجاح', 'success');
}

// إعداد البحث عن الموظف في التقارير
function setupReportEmployeeSearch() {
  const reportEmployeeInput = document.getElementById('reportEmployee');
  const reportEmployeeSuggestions = document.getElementById('reportEmployeeSuggestions');

  if (reportEmployeeInput && reportEmployeeSuggestions) {
    reportEmployeeInput.addEventListener('input', function() {
      handleEmployeeSearch(this.value, 'reportEmployeeSuggestions');
    });

    // إضافة مستمع للتغيير لملء الفلتر
    reportEmployeeInput.addEventListener('change', function() {
      // لا حاجة لملء حقول إضافية في التقارير
    });
  }
}

// وظائف البحث المتقدم
function setupAdvancedSearch() {
  // البحث في الجدول الرئيسي
  const searchInput = document.createElement('input');
  searchInput.type = 'text';
  searchInput.id = 'tableSearch';
  searchInput.placeholder = 'البحث في الجدول...';
  searchInput.className = 'table-search-input';

  // إضافة حقل البحث إلى عنصر التحكم في الجدول
  const tableControls = document.querySelector('.table-controls');
  if (tableControls) {
    tableControls.insertBefore(searchInput, tableControls.firstChild);

    // إضافة مستمع للبحث
    searchInput.addEventListener('input', function() {
      filterTable(this.value);
    });
  }
}

// تصفية الجدول
function filterTable(searchTerm) {
  const tableBody = document.getElementById('idealEmployeesTableBody');
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll('tr');
  const searchLower = searchTerm.toLowerCase();

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    let found = false;

    // البحث في جميع الخلايا
    cells.forEach(cell => {
      if (cell.textContent.toLowerCase().includes(searchLower)) {
        found = true;
      }
    });

    // إظهار أو إخفاء الصف
    row.style.display = found ? '' : 'none';
  });
}

// تحسين وظائف التصدير
function exportIdealEmployeesToExcelAdvanced() {
  if (idealEmployees.length === 0) {
    showNotification('لا توجد بيانات للتصدير', 'error');
    return;
  }

  // إضافة إحصائيات إلى التصدير
  const statistics = {
    'إجمالي العمال المثاليين': idealEmployees.length,
    'إجمالي المكافآت': idealEmployees.reduce((sum, item) => sum + parseFloat(item.reward_amount), 0).toLocaleString('ar-SA') + ' ريال',
    'متوسط التقييم': (idealEmployees.reduce((sum, item) => sum + parseFloat(item.evaluation_score), 0) / idealEmployees.length).toFixed(2),
    'أعلى تقييم': Math.max(...idealEmployees.map(item => parseFloat(item.evaluation_score))),
    'أقل تقييم': Math.min(...idealEmployees.map(item => parseFloat(item.evaluation_score)))
  };

  const data = idealEmployees.map(idealEmployee => ({
    'كود الموظف': idealEmployee.employee_code,
    'الاسم': idealEmployee.employee_name,
    'الإدارة': idealEmployee.department,
    'من فترة': formatDate(idealEmployee.from_period),
    'إلى فترة': formatDate(idealEmployee.to_period),
    'درجة التقييم': idealEmployee.evaluation_score,
    'مبلغ المكافأة': idealEmployee.reward_amount,
    'سبب الاختيار': idealEmployee.selection_reason,
    'ملاحظات': idealEmployee.notes || '',
    'تاريخ الإضافة': formatDate(idealEmployee.created_at)
  }));

  // إنشاء ورقة البيانات
  const ws = XLSX.utils.json_to_sheet(data);

  // إنشاء ورقة الإحصائيات
  const statsData = Object.entries(statistics).map(([key, value]) => ({
    'البيان': key,
    'القيمة': value
  }));
  const statsWs = XLSX.utils.json_to_sheet(statsData);

  // إنشاء المصنف
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'العمال المثاليين');
  XLSX.utils.book_append_sheet(wb, statsWs, 'الإحصائيات');

  const fileName = `العمال_المثاليين_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(wb, fileName);

  showNotification('تم تصدير البيانات مع الإحصائيات بنجاح', 'success');
}

// تحسين وظائف التقارير
function generateAdvancedReport() {
  const startDate = document.getElementById('reportStartDate').value;
  const endDate = document.getElementById('reportEndDate').value;
  const department = document.getElementById('reportDepartment').value;
  const employee = document.getElementById('reportEmployee').value;

  let filteredData = [...idealEmployees];

  // تطبيق الفلاتر مع تحسينات
  if (startDate) {
    filteredData = filteredData.filter(item =>
      new Date(item.from_period) >= new Date(startDate)
    );
  }

  if (endDate) {
    filteredData = filteredData.filter(item =>
      new Date(item.to_period) <= new Date(endDate)
    );
  }

  if (department) {
    filteredData = filteredData.filter(item =>
      item.department === department
    );
  }

  if (employee) {
    const employeeCode = employee.split(' - ')[0];
    const employeeName = employee.split(' - ')[1];
    filteredData = filteredData.filter(item =>
      item.employee_code === employeeCode ||
      (employeeName && item.employee_name.toLowerCase().includes(employeeName.toLowerCase())) ||
      item.employee_name.toLowerCase().includes(employee.toLowerCase())
    );
  }

  // ترتيب البيانات حسب التاريخ
  filteredData.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

  // عرض النتائج
  displayReportResults(filteredData);

  // تحديث الإحصائيات
  updateAdvancedReportStatistics(filteredData);
}

// تحديث إحصائيات التقرير المتقدمة
function updateAdvancedReportStatistics(data) {
  updateReportStatistics(data);

  // إضافة إحصائيات إضافية
  if (data.length > 0) {
    // أعلى وأقل تقييم
    const scores = data.map(item => parseFloat(item.evaluation_score));
    const maxScore = Math.max(...scores);
    const minScore = Math.min(...scores);

    // إضافة معلومات إضافية للإحصائيات
    const statsContainer = document.querySelector('.report-statistics');
    if (statsContainer) {
      // إزالة الإحصائيات الإضافية السابقة
      const existingExtra = statsContainer.querySelectorAll('.extra-stat');
      existingExtra.forEach(stat => stat.remove());

      // إضافة إحصائيات جديدة
      const extraStats = [
        { icon: 'fas fa-arrow-up', label: 'أعلى تقييم', value: maxScore },
        { icon: 'fas fa-arrow-down', label: 'أقل تقييم', value: minScore },
        { icon: 'fas fa-building', label: 'عدد الإدارات', value: [...new Set(data.map(item => item.department))].length }
      ];

      extraStats.forEach(stat => {
        const statCard = document.createElement('div');
        statCard.className = 'stat-card extra-stat';
        statCard.innerHTML = `
          <div class="stat-icon">
            <i class="${stat.icon}"></i>
          </div>
          <div class="stat-info">
            <h4>${stat.label}</h4>
            <span>${stat.value}</span>
          </div>
        `;
        statsContainer.appendChild(statCard);
      });
    }
  }
}

// إعداد إضافي للبحث والتقارير
function setupAdditionalFeatures() {
  setTimeout(() => {
    setupReportEmployeeSearch();
    setupAdvancedSearch();

    // استبدال وظائف التصدير بالنسخ المحسنة
    const exportBtn = document.getElementById('exportIdealEmployees');
    if (exportBtn) {
      exportBtn.removeEventListener('click', exportIdealEmployeesToExcel);
      exportBtn.addEventListener('click', exportIdealEmployeesToExcelAdvanced);
    }

    // استبدال وظيفة إنشاء التقرير
    const generateReportBtn = document.getElementById('generateReport');
    if (generateReportBtn) {
      generateReportBtn.removeEventListener('click', generateReport);
      generateReportBtn.addEventListener('click', generateAdvancedReport);
    }
  }, 500);
}

// التحقق من المحتوى المحدد من البطاقات
function checkSelectedContent() {
  console.log('التحقق من المحتوى المحدد...');

  const selectedContent = localStorage.getItem('selectedIdealEmployeeTab');
  console.log('المحتوى المحفوظ:', selectedContent);

  if (selectedContent) {
    // حذف المحتوى المحفوظ
    localStorage.removeItem('selectedIdealEmployeeTab');

    // التحقق من صحة المحتوى المحدد
    if (selectedContent === 'add-ideal-employee' || selectedContent === 'reports') {
      // عرض المحتوى المناسب
      showContent(selectedContent);
    } else {
      console.warn('محتوى غير صحيح:', selectedContent);
      // عرض المحتوى الافتراضي
      showContent('add-ideal-employee');
    }
  } else {
    // عرض المحتوى الافتراضي (إضافة عامل مثالي)
    console.log('عرض المحتوى الافتراضي');
    showContent('add-ideal-employee');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // التأكد من أن DOM جاهز
  if (document.readyState !== 'complete') {
    setTimeout(() => showContent(contentType), 50);
    return;
  }

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  console.log('عدد التبويبات الموجودة:', allContents.length);

  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  console.log('العنصر المستهدف:', targetContent);

  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';
    console.log('تم عرض المحتوى بنجاح:', contentType);

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-ideal-employee') {
        pageTitle.textContent = 'إضافة عامل مثالي';
      } else if (contentType === 'reports') {
        pageTitle.textContent = 'تقارير العامل المثالي';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'reports') {
      console.log('🔄 معالجة تبويب التقارير...');
      console.log('عدد العمال المثاليين الحالي:', idealEmployees.length);

      // تحميل بيانات التقارير إذا لم تكن محملة
      if (idealEmployees.length === 0) {
        console.log('📥 تحميل بيانات العمال المثاليين...');
        loadIdealEmployees().then(() => {
          console.log('✅ تم تحميل البيانات، عدد السجلات:', idealEmployees.length);
          // عرض جميع البيانات في جدول التقارير بعد التحميل
          displayReportResults(idealEmployees);
          updateReportStatistics(idealEmployees);
        }).catch(error => {
          console.error('❌ خطأ في تحميل البيانات:', error);
          // عرض جدول فارغ مع رسالة
          displayReportResults([]);
          updateReportStatistics([]);
        });
      } else {
        console.log('📊 عرض البيانات الموجودة في جدول التقارير...');
        // عرض البيانات الموجودة في جدول التقارير
        displayReportResults(idealEmployees);
        updateReportStatistics(idealEmployees);
      }
    }
  }
}

// إعداد البحث المباشر
function setupFilters() {
  const filterInputs = ['filterEmployeeCode', 'filterEmployeeName', 'filterFromDate', 'filterToDate'];

  filterInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', function() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(() => {
          applyFilters();
        }, 300);
      });
    }
  });

  // زر مسح الفلاتر
  const clearBtn = document.getElementById('clearFiltersBtn');
  if (clearBtn) {
    clearBtn.addEventListener('click', clearFilters);
  }
}

// تطبيق الفلاتر
function applyFilters() {
  const employeeCode = document.getElementById('filterEmployeeCode')?.value.trim() || '';
  const employeeName = document.getElementById('filterEmployeeName')?.value.trim() || '';
  const fromDate = document.getElementById('filterFromDate')?.value || '';
  const toDate = document.getElementById('filterToDate')?.value || '';

  const tableBody = document.querySelector('.ideal-employees-table tbody');
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return;

    // استخراج البيانات من الخلايا
    const rowEmployeeCode = cells[0]?.textContent?.trim() || '';
    const rowEmployeeName = cells[1]?.textContent?.trim() || '';
    const rowFromDate = cells[3]?.textContent?.trim() || '';
    const rowToDate = cells[4]?.textContent?.trim() || '';

    let showRow = true;

    // فلترة بالكود
    if (employeeCode && !rowEmployeeCode.includes(employeeCode)) {
      showRow = false;
    }

    // فلترة بالاسم
    if (employeeName && !rowEmployeeName.toLowerCase().includes(employeeName.toLowerCase())) {
      showRow = false;
    }

    // فلترة بالتاريخ من
    if (fromDate && rowFromDate) {
      const rowDateObj = new Date(rowFromDate);
      const fromDateObj = new Date(fromDate);
      if (rowDateObj < fromDateObj) {
        showRow = false;
      }
    }

    // فلترة بالتاريخ إلى
    if (toDate && rowToDate) {
      const rowDateObj = new Date(rowToDate);
      const toDateObj = new Date(toDate);
      if (rowDateObj > toDateObj) {
        showRow = false;
      }
    }

    // إظهار أو إخفاء الصف
    row.style.display = showRow ? '' : 'none';
  });
}

// مسح الفلاتر
function clearFilters() {
  document.getElementById('filterEmployeeCode').value = '';
  document.getElementById('filterEmployeeName').value = '';
  document.getElementById('filterFromDate').value = '';
  document.getElementById('filterToDate').value = '';

  // إظهار جميع الصفوف
  const tableBody = document.querySelector('.ideal-employees-table tbody');
  if (tableBody) {
    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
      row.style.display = '';
    });
  }
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  // تنظيف البيانات المؤقتة
  localStorage.removeItem('debugMode');
  localStorage.removeItem('debugMockData');
  localStorage.removeItem('testData');
  localStorage.removeItem('sampleData');

  initializeIdealEmployeeJS();

  // إعداد البحث المباشر
  setupFilters();
});
